using UnityEngine;

public class UIMarginTester : MonoBehaviour
{
    [Header("测试设置")]
    public bool enableTesting = true;
    public KeyCode toggleMarginsKey = KeyCode.T;
    public KeyCode toggleCameraLimitKey = KeyCode.L;
    public KeyCode toggleGizmosKey = KeyCode.G;

    private MapScene mapScene;

    void Start()
    {
        mapScene = FindObjectOfType<MapScene>();
        if (mapScene == null)
        {
            Debug.LogError("未找到MapScene组件！");
        }
    }

    void Update()
    {
        if (!enableTesting || mapScene == null) return;

        if (Input.GetKeyDown(toggleMarginsKey))
        {
            // 切换边距线条显示
            mapScene.showUIMargins = !mapScene.showUIMargins;
            Debug.Log($"UI边距线条显示: {mapScene.showUIMargins}");
        }

        if (Input.GetKeyDown(toggleCameraLimitKey))
        {
            // 切换相机限制功能
            mapScene.useMarginsForCameraLimit = !mapScene.useMarginsForCameraLimit;
            Debug.Log($"使用边距限制相机: {mapScene.useMarginsForCameraLimit}");
        }

        if (Input.GetKeyDown(toggleGizmosKey))
        {
            // 切换Gizmos显示
            mapScene.showCameraLimitGizmos = !mapScene.showCameraLimitGizmos;
            Debug.Log($"显示相机限制Gizmos: {mapScene.showCameraLimitGizmos}");
        }
    }

    void OnGUI()
    {
        if (!enableTesting) return;

        GUILayout.BeginArea(new Rect(10, 10, 350, 280));
        GUILayout.Label("FairyGUI边距映射测试");
        GUILayout.Space(10);

        GUILayout.Label("控制键:");
        GUILayout.Label($"按 {toggleMarginsKey} 键切换边距线条显示");
        GUILayout.Label($"按 {toggleCameraLimitKey} 键切换相机限制功能");
        GUILayout.Label($"按 {toggleGizmosKey} 键切换Gizmos显示");
        GUILayout.Space(10);

        if (mapScene != null)
        {
            GUILayout.Label("当前状态:");
            GUILayout.Label($"边距线条显示: {mapScene.showUIMargins}");
            GUILayout.Label($"使用边距限制相机: {mapScene.useMarginsForCameraLimit}");
            GUILayout.Label($"显示限制Gizmos: {mapScene.showCameraLimitGizmos}");
            GUILayout.Space(10);

            GUILayout.Label($"屏幕分辨率: {Screen.width}x{Screen.height}");
            GUILayout.Label("FairyGUI设计分辨率: 1080x1920");
            GUILayout.Label("上边距: 300像素");
            GUILayout.Label("下边距: 500像素");

            if (mapScene.mainCamera != null)
            {
                Vector3 camPos = mapScene.mainCamera.transform.position;
                GUILayout.Label($"相机位置: ({camPos.x:F2}, {camPos.y:F2})");
            }
        }

        GUILayout.EndArea();
    }
}
