using UnityEngine;
using UnityEngine.Tilemaps;
using System;
using System.Collections;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;
    public GameObject flyTarget;

    public static event Action<Tilemap, Vector3Int, TileBase> OnTileClickedEvent;

    [Header("拖动设置")]
    public float dragSpeed = 1f;
    public float dragThreshold = 10f; // 拖拽阈值，像素单位

    [Header("缩放设置")]
    public float zoomSpeed = 5f;
    public float minZoom = 10f;
    private float maxZoom;

    [Header("地图边界设置")]
    public int mapWidth = 4096;
    public int mapHeight = 4096;
    public Vector2 mapCenter = Vector2.zero;

    [Header("飞行动画设置")]
    public float flyDuration = 1f;
    public AnimationCurve flyCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    public bool removeTileOnClick = true;
    public AudioClip tileClickSound;

    [Header("FairyGUI边距映射")]
    public bool showUIMargins = true;
    public Color marginLineColor = Color.red;
    public float marginLineWidth = 2f;
    public bool useMarginsForCameraLimit = true;
    public bool showCameraLimitGizmos = true;

    // FairyGUI设计分辨率
    private const int FAIRYGUI_DESIGN_WIDTH = 1080;
    private const int FAIRYGUI_DESIGN_HEIGHT = 1920;
    private const float TOP_MARGIN = 300f;
    private const float BOTTOM_MARGIN = 500f;

    public Camera mainCamera;
    private bool isDragging = false;
    private bool isPotentialDrag = false; // 是否可能开始拖拽
    private Vector3 lastMousePosition;
    private Vector3 dragStartPosition; // 拖拽开始位置

    // 触摸缩放相关变量
    private bool isTouchZooming = false;
    private float lastTouchDistance = 0f;

    // 边距线条对象
    private LineRenderer topMarginLine;
    private LineRenderer bottomMarginLine;

    void Start()
    {
        InitializeComponents();
    }

    void Update()
    {
        HandleInput();
        UpdateMarginLines();
    }

    void InitializeComponents()
    {
        maxZoom = mainCamera.orthographicSize;

        // 创建边距线条
        CreateMarginLines();
    }

    void HandleInput()
    {
        HandleZoom();
        HandleTouchInput();

        if (Input.GetMouseButtonDown(0))
        {
            if (!isTouchZooming)
            {
                StartPotentialDrag();
            }
        }
        else if (Input.GetMouseButton(0))
        {
            if (isPotentialDrag && !isDragging)
            {
                CheckDragThreshold();
            }
            else if (isDragging)
            {
                UpdateDrag();
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            HandleClickAndEndDrag();
        }
    }

    void StartPotentialDrag()
    {
        isPotentialDrag = true;
        isDragging = false;
        dragStartPosition = Input.mousePosition;
        lastMousePosition = Input.mousePosition;
    }

    void CheckDragThreshold()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        float dragDistance = Vector3.Distance(dragStartPosition, currentMousePosition);

        if (dragDistance >= dragThreshold)
        {
            isDragging = true;
            isPotentialDrag = false;
            lastMousePosition = currentMousePosition;
        }
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        // 将屏幕坐标差值转换为世界坐标差值
        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(mouseDelta) - mainCamera.ScreenToWorldPoint(Vector3.zero);

        // 只在X和Y轴移动，保持Z坐标不变
        Vector3 newPosition = mainCamera.transform.position + new Vector3(worldDelta.x * dragSpeed, worldDelta.y * dragSpeed, 0);

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void HandleClickAndEndDrag()
    {
        // 只有在没有发生拖拽的情况下才处理点击
        if (!isDragging && (isPotentialDrag || !isTouchZooming))
        {
            HandleClick();
        }

        EndDrag();
    }

    void EndDrag()
    {
        isDragging = false;
        isPotentialDrag = false;
    }

    void HandleZoom()
    {
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            float currentSize = mainCamera.orthographicSize;
            float newSize = currentSize - scroll * zoomSpeed;
            newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

            mainCamera.orthographicSize = newSize;

            // 缩放后重新限制相机位置
            Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
            mainCamera.transform.position = clampedPosition;
        }
    }

    void HandleTouchInput()
    {
        if (Input.touchCount == 2)
        {
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);

            float currentTouchDistance = Vector2.Distance(touch1.position, touch2.position);

            if (!isTouchZooming)
            {
                isTouchZooming = true;
                lastTouchDistance = currentTouchDistance;
                isDragging = false;
                isPotentialDrag = false;
            }
            else
            {
                float deltaDistance = currentTouchDistance - lastTouchDistance;
                float zoomFactor = deltaDistance * 0.01f; // 调整缩放敏感度

                float currentSize = mainCamera.orthographicSize;
                float newSize = currentSize - zoomFactor * zoomSpeed;
                newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

                mainCamera.orthographicSize = newSize;

                // 缩放后重新限制相机位置
                Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
                mainCamera.transform.position = clampedPosition;

                lastTouchDistance = currentTouchDistance;
            }
        }
        else
        {
            isTouchZooming = false;
        }
    }

    void HandleClick()
    {
        Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
        mouseWorldPos.z = 0f;
        RaycastHit2D hit = Physics2D.Raycast(mouseWorldPos, Vector2.zero);
        if (hit.collider != null)
        {
            if (hit.collider.TryGetComponent(out PolygonCollider2D collider))
            {
                if (collider != null)
                {
                    OnTileClicked(collider.gameObject);
                    Destroy(collider.gameObject);
                }
            }
        }
    }

    void OnTileClicked(GameObject tileGo)
    {
        if (flyTarget != null)
        {
            StartFlyAnimation(tileGo);
        }
    }

    void StartFlyAnimation(GameObject tilemapObject)
    {
        // 创建一个临时的飞行对象来表示被点击的瓦片
        GameObject flyObject = new GameObject("FlyingTile");

        // 添加SpriteRenderer来显示瓦片
        SpriteRenderer spriteRenderer = flyObject.AddComponent<SpriteRenderer>();

        // 获取瓦片的精灵
        var srcRenderer = tilemapObject.GetComponent<SpriteRenderer>();
        spriteRenderer.sprite = srcRenderer.sprite;

        var startPos = tilemapObject.transform.position;
        flyObject.transform.position = startPos;

        // 开始飞行协程
        StartCoroutine(FlyToTarget(flyObject, spriteRenderer, startPos, flyTarget.transform.position));
    }

    IEnumerator FlyToTarget(GameObject flyObject, SpriteRenderer spriteRenderer, Vector3 startPos, Vector3 targetPos)
    {
        float elapsedTime = 0f;
        Vector3 initialScale = flyObject.transform.localScale;

        while (elapsedTime < flyDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / flyDuration;
            float curveValue = flyCurve.Evaluate(progress);

            // 线性插值位置
            Vector3 currentPos = Vector3.Lerp(startPos, targetPos, curveValue);

            // 添加抛物线效果
            float height = Mathf.Sin(progress * Mathf.PI) * 2f;
            currentPos.y += height;

            flyObject.transform.position = currentPos;

            // 添加旋转效果
            flyObject.transform.Rotate(0, 0, 360f * Time.deltaTime);

            // 添加缩放效果，飞行过程中逐渐变小
            float scale = Mathf.Lerp(1f, 0.3f, progress);
            flyObject.transform.localScale = initialScale * scale;

            // 添加透明度变化
            if (spriteRenderer != null)
            {
                Color color = spriteRenderer.color;
                color.a = Mathf.Lerp(1f, 0.5f, progress);
                spriteRenderer.color = color;
            }

            yield return null;
        }

        // 动画结束后销毁飞行对象
        Destroy(flyObject);
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;

        float minX, maxX, minY, maxY;

        if (useMarginsForCameraLimit)
        {
            // 使用FairyGUI边距线来限制相机移动范围
            Vector2 topMarginWorld = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH * 0.5f, TOP_MARGIN);
            Vector2 bottomMarginWorld = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH * 0.5f, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);

            // 计算相机Y轴限制范围
            // 相机中心不能超过上边距线（考虑相机视野的一半高度）
            maxY = topMarginWorld.y - cameraHeight * 0.5f;
            // 相机中心不能低于下边距线（考虑相机视野的一半高度）
            minY = bottomMarginWorld.y + cameraHeight * 0.5f;

            // X轴仍使用原来的地图边界限制
            minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
            maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
        }
        else
        {
            // 使用原来的地图边界限制
            minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
            maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
            minY = mapCenter.y - mapHeight * 0.005f + cameraHeight * 0.5f;
            maxY = mapCenter.y + mapHeight * 0.005f - cameraHeight * 0.5f;
        }

        // 如果地图比相机视野小，固定在中心
        if (maxX < minX)
        {
            minX = maxX = mapCenter.x;
        }

        if (maxY < minY)
        {
            float centerY = (minY + maxY) * 0.5f;
            minY = maxY = centerY;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }

    // 创建边距线条
    void CreateMarginLines()
    {
        if (!showUIMargins) return;

        // 创建上边距线条
        GameObject topLineObj = new GameObject("TopMarginLine");
        topLineObj.transform.SetParent(transform);
        topMarginLine = topLineObj.AddComponent<LineRenderer>();
        SetupLineRenderer(topMarginLine, Color.red);

        // 创建下边距线条
        GameObject bottomLineObj = new GameObject("BottomMarginLine");
        bottomLineObj.transform.SetParent(transform);
        bottomMarginLine = bottomLineObj.AddComponent<LineRenderer>();
        SetupLineRenderer(bottomMarginLine, Color.green);

        // 初始更新线条位置
        UpdateMarginLines();
    }

    // 设置LineRenderer属性
    void SetupLineRenderer(LineRenderer lineRenderer, Color lineColor)
    {
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = lineRenderer.endColor = lineColor;
        lineRenderer.startWidth = marginLineWidth * 0.01f;
        lineRenderer.endWidth = marginLineWidth * 0.01f;
        lineRenderer.positionCount = 2;
        lineRenderer.useWorldSpace = true;
        lineRenderer.sortingOrder = 1000;
    }

    // 更新边距线条位置
    void UpdateMarginLines()
    {
        if (topMarginLine == null || bottomMarginLine == null) return;

        // 控制线条显示/隐藏
        bool shouldShow = showUIMargins;
        topMarginLine.enabled = shouldShow;
        bottomMarginLine.enabled = shouldShow;

        if (!shouldShow) return;

        // 计算上边距线条的世界坐标
        Vector2 topLeft = ConvertFairyGUIToWorldPosition(0, TOP_MARGIN);
        Vector2 topRight = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH, TOP_MARGIN);

        // 计算下边距线条的世界坐标
        Vector2 bottomLeft = ConvertFairyGUIToWorldPosition(0, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);
        Vector2 bottomRight = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);

        // 设置上边距线条位置
        topMarginLine.SetPosition(0, new Vector3(topLeft.x, topLeft.y, 0));
        topMarginLine.SetPosition(1, new Vector3(topRight.x, topRight.y, 0));

        // 设置下边距线条位置
        bottomMarginLine.SetPosition(0, new Vector3(bottomLeft.x, bottomLeft.y, 0));
        bottomMarginLine.SetPosition(1, new Vector3(bottomRight.x, bottomRight.y, 0));
    }

    // 将FairyGUI像素坐标转换为世界坐标
    Vector2 ConvertFairyGUIToWorldPosition(float fairyGUIX, float fairyGUIY)
    {
        if (mainCamera == null) return Vector2.zero;

        // 获取当前屏幕尺寸
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;

        // 计算FairyGUI的缩放因子
        float scaleX = screenWidth / FAIRYGUI_DESIGN_WIDTH;
        float scaleY = screenHeight / FAIRYGUI_DESIGN_HEIGHT;

        // 使用较小的缩放因子保持比例
        float scale = Mathf.Min(scaleX, scaleY);

        // 计算实际屏幕坐标
        float actualScreenX = fairyGUIX * scale;
        float actualScreenY = fairyGUIY * scale;

        // 如果有居中偏移，需要计算偏移量
        float offsetX = (screenWidth - FAIRYGUI_DESIGN_WIDTH * scale) * 0.5f;
        float offsetY = (screenHeight - FAIRYGUI_DESIGN_HEIGHT * scale) * 0.5f;

        // actualScreenX += offsetX;
        // actualScreenY += offsetY;

        // 转换为世界坐标
        Vector3 screenPos = new Vector3(actualScreenX, screenHeight - actualScreenY, mainCamera.nearClipPlane);
        Vector3 worldPos = mainCamera.ScreenToWorldPoint(screenPos);

        return new Vector2(worldPos.x, worldPos.y);
    }

    // 绘制UI边距线条和相机限制范围
    void OnDrawGizmos()
    {
        if (mainCamera == null) return;

        // 绘制UI边距线条
        if (showUIMargins)
        {
            Gizmos.color = marginLineColor;

            // 计算上边距线条的世界坐标
            Vector2 topLeft = ConvertFairyGUIToWorldPosition(0, TOP_MARGIN);
            Vector2 topRight = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH, TOP_MARGIN);

            // 计算下边距线条的世界坐标
            Vector2 bottomLeft = ConvertFairyGUIToWorldPosition(0, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);
            Vector2 bottomRight = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);

            // 绘制上边距线条
            Gizmos.DrawLine(new Vector3(topLeft.x, topLeft.y, 0), new Vector3(topRight.x, topRight.y, 0));

            // 绘制下边距线条
            Gizmos.DrawLine(new Vector3(bottomLeft.x, bottomLeft.y, 0), new Vector3(bottomRight.x, bottomRight.y, 0));
        }

        // 绘制相机限制范围
        if (showCameraLimitGizmos && useMarginsForCameraLimit)
        {
            float cameraHeight = mainCamera.orthographicSize * 2f;
            float cameraWidth = cameraHeight * mainCamera.aspect;

            Vector2 topMarginWorld = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH * 0.5f, TOP_MARGIN);
            Vector2 bottomMarginWorld = ConvertFairyGUIToWorldPosition(FAIRYGUI_DESIGN_WIDTH * 0.5f, FAIRYGUI_DESIGN_HEIGHT - BOTTOM_MARGIN);

            // 计算相机中心的限制范围
            float maxY = topMarginWorld.y - cameraHeight * 0.5f;
            float minY = bottomMarginWorld.y + cameraHeight * 0.5f;
            float minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
            float maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;

            // 绘制相机限制区域
            Gizmos.color = Color.yellow;
            Vector3[] corners = {
                new Vector3(minX, minY, 0),
                new Vector3(maxX, minY, 0),
                new Vector3(maxX, maxY, 0),
                new Vector3(minX, maxY, 0)
            };

            // 绘制限制区域边框
            for (int i = 0; i < 4; i++)
            {
                Gizmos.DrawLine(corners[i], corners[(i + 1) % 4]);
            }

            // 绘制当前相机位置
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireCube(mainCamera.transform.position, new Vector3(0.5f, 0.5f, 0.5f));
        }
    }
}